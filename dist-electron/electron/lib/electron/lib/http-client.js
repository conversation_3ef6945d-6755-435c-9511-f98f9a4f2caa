"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeHttpRequest = makeHttpRequest;
const https = __importStar(require("https"));
const http = __importStar(require("http"));
const querystring = __importStar(require("querystring"));
/**
 * HTTP请求工具函数
 * 支持GET和POST请求，自动处理JSON响应
 */
function makeHttpRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        try {
            const parsedUrl = new URL(url);
            const httpModule = parsedUrl.protocol === 'https:' ? https : http;
            // 添加查询参数
            if (options.params) {
                const queryString = querystring.stringify(options.params);
                if (queryString) {
                    parsedUrl.search = parsedUrl.search
                        ? `${parsedUrl.search}&${queryString}`
                        : `?${queryString}`;
                }
            }
            const requestOptions = {
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
                path: parsedUrl.pathname + parsedUrl.search,
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Electron-App/1.0.0',
                    ...options.headers
                },
                timeout: options.timeout || 10000
            };
            const req = httpModule.request(requestOptions, (res) => {
                const chunks = [];
                res.on('data', (chunk) => {
                    chunks.push(chunk);
                });
                res.on('end', () => {
                    try {
                        const statusCode = res.statusCode || 0;
                        if (statusCode >= 400) {
                            reject(new Error(`HTTP Error: ${statusCode} ${res.statusMessage}`));
                            return;
                        }
                        const responseBuffer = Buffer.concat(chunks);
                        let parsedData;
                        // 根据 responseType 处理响应数据
                        if (options.responseType === 'buffer') {
                            parsedData = responseBuffer;
                        }
                        else {
                            const responseData = responseBuffer.toString('utf8');
                            const contentType = res.headers['content-type'] || '';
                            if (options.responseType === 'json' || (contentType.includes('application/json') && responseData.trim())) {
                                try {
                                    parsedData = JSON.parse(responseData);
                                }
                                catch (parseError) {
                                    parsedData = responseData;
                                }
                            }
                            else {
                                parsedData = responseData;
                            }
                        }
                        resolve({
                            data: parsedData,
                            status: statusCode,
                            statusText: res.statusMessage || 'OK',
                            headers: res.headers
                        });
                    }
                    catch (error) {
                        reject(new Error(`Response parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`));
                    }
                });
            });
            req.on('error', (error) => {
                reject(new Error(`Network error: ${error.message}`));
            });
            req.on('timeout', () => {
                req.destroy();
                reject(new Error(`Request timeout after ${requestOptions.timeout}ms`));
            });
            if (options.data && options.method !== 'GET') {
                const postData = typeof options.data === 'object' ? JSON.stringify(options.data) : String(options.data);
                req.write(postData);
            }
            req.end();
        }
        catch (error) {
            reject(new Error(`Request error: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
    });
}
