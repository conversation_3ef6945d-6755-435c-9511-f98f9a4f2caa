"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadImages = downloadImages;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const http_client_1 = require("./http-client");
/**
 * 下载图片到指定路径
 * @param imageUrls 图片URL映射对象，格式如 {"64:192": "https://..."}
 * @param downloadPath 下载路径
 * @returns Promise<DownloadResult>
 */
async function downloadImages({ imageUrls, downloadPath }) {
    try {
        // 确保下载目录存在
        if (!fs.existsSync(downloadPath)) {
            fs.mkdirSync(downloadPath, { recursive: true });
        }
        const filePaths = [];
        const downloadPromises = [];
        // 为每个图片URL创建下载任务
        for (const [nodeId, imageUrl] of Object.entries(imageUrls)) {
            if (!imageUrl)
                continue;
            const downloadPromise = downloadSingleImage(nodeId, imageUrl, downloadPath)
                .then(filePath => {
                if (filePath) {
                    filePaths.push(filePath);
                }
            })
                .catch(error => {
                console.error(`下载图片 ${nodeId} 失败:`, error);
                // 不抛出错误，继续下载其他图片
            });
            downloadPromises.push(downloadPromise);
        }
        // 等待所有下载完成
        await Promise.all(downloadPromises);
        return {
            success: true,
            data: {
                filePaths
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
/**
 * 下载单个图片
 * @param nodeId 节点ID
 * @param imageUrl 图片URL
 * @param downloadPath 下载路径
 * @returns Promise<string | null> 返回文件路径或null（如果失败）
 */
async function downloadSingleImage(nodeId, imageUrl, downloadPath) {
    try {
        // 获取图片数据
        const response = await (0, http_client_1.makeHttpRequest)(imageUrl, {
            responseType: 'buffer'
        });
        // 生成文件名：将冒号替换为短横线，格式如 64-192.png
        const fileName = `${nodeId.replace(':', '-')}.png`;
        const filePath = path.join(downloadPath, fileName);
        // 写入文件
        fs.writeFileSync(filePath, response.data);
        console.log(`图片下载成功: ${fileName}`);
        return filePath;
    }
    catch (error) {
        console.error(`下载图片 ${nodeId} 失败:`, error);
        return null;
    }
}
