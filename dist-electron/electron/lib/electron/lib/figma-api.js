"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.figmaApiRequest = figmaApiRequest;
exports.parseFigmaUrl = parseFigmaUrl;
exports.getFigmaNodeInfoFromUrl = getFigmaNodeInfoFromUrl;
const http_client_1 = require("./http-client");
// Figma API配置
const FIGMA_TOKEN = "*********************************************";
/**
 * 发送 Figma API 请求
 */
async function figmaApiRequest({ url, params, headers }) {
    try {
        const response = await (0, http_client_1.makeHttpRequest)(url, {
            params,
            headers: {
                'X-FIGMA-TOKEN': FIGMA_TOKEN,
                ...headers
            }
        });
        return { success: true, data: response.data };
    }
    catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
/**
 * 解析 Figma 链接，提取文件密钥和节点ID
 */
async function parseFigmaUrl(url) {
    try {
        const parsedUrl = new URL(url);
        if (!parsedUrl.hostname.includes('figma.com')) {
            throw new Error('不是有效的 Figma 链接');
        }
        const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0);
        if (pathParts.length < 2 || !['file', 'design'].includes(pathParts[0])) {
            throw new Error('无效的 Figma 链接格式');
        }
        const fileKey = pathParts[1];
        let nodeId;
        const nodeIdParam = parsedUrl.searchParams.get('node-id');
        if (nodeIdParam) {
            nodeId = decodeURIComponent(nodeIdParam).replace(':', '-');
        }
        return {
            success: true,
            data: {
                fileKey,
                nodeId,
                originalUrl: url
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
/**
 * 递归查找指定ID的节点
 * @param node 当前节点
 * @param targetId 目标节点ID
 * @returns 找到的节点或null
 */
function findNodeById(node, targetId) {
    if (!node || typeof node !== 'object') {
        return null;
    }
    // 检查当前节点是否匹配
    if (node.id === targetId) {
        return node;
    }
    // 递归搜索子节点
    if (node.children && Array.isArray(node.children)) {
        for (const child of node.children) {
            const found = findNodeById(child, targetId);
            if (found) {
                return found;
            }
        }
    }
    return null;
}
/**
 * 清理节点数据，删除指定字段
 * @param node 原始节点
 * @returns 清理后的节点
 */
function cleanNodeData(node) {
    if (!node || typeof node !== 'object') {
        return node;
    }
    // 创建节点的浅拷贝
    const cleanedNode = { ...node };
    // 删除指定字段
    delete cleanedNode.children;
    delete cleanedNode.components;
    delete cleanedNode.componentSets;
    return cleanedNode;
}
/**
 * 根据 Figma 链接获取节点信息 (主进程版本)
 * 与渲染进程的 getFigmaInfoFromUrl 保持一致的处理逻辑
 * @param figmaUrl Figma 分享链接
 * @param nodeId 节点ID
 * @returns Promise<ApiResponse>
 */
async function getFigmaNodeInfoFromUrl(figmaUrl, nodeId) {
    try {
        // 解析链接获取文件key
        const parseResult = await parseFigmaUrl(figmaUrl);
        if (!parseResult.success) {
            throw new Error(`解析Figma URL失败: ${parseResult.error}`);
        }
        // 获取完整的文件信息（与渲染进程保持一致）
        const fileInfoUrl = `https://api.figma.com/v1/files/${parseResult.data.fileKey}`;
        const result = await figmaApiRequest({
            url: fileInfoUrl
        });
        if (!result.success) {
            throw new Error(`获取Figma文件信息失败: ${result.error}`);
        }
        const fileData = result.data;
        let targetNode = null;
        // 查找指定节点（与渲染进程保持一致的查找逻辑）
        console.log(`🔍 查找节点ID: ${nodeId}`);
        // 首先检查是否直接在 nodes 中
        if (fileData.nodes && fileData.nodes[nodeId]) {
            console.log(`✅ 在 nodes[${nodeId}] 中找到节点`);
            targetNode = fileData.nodes[nodeId].document;
        }
        else {
            console.log(`❌ 在 nodes[${nodeId}] 中未找到节点`);
            // 如果不在直接节点中，在所有节点的文档中递归查找
            if (fileData.nodes) {
                console.log(`🔄 开始在 nodes 中递归查找...`);
                for (const nodeKey of Object.keys(fileData.nodes)) {
                    const nodeInfo = fileData.nodes[nodeKey];
                    if (nodeInfo && nodeInfo.document) {
                        console.log(`  检查节点: ${nodeKey}`);
                        targetNode = findNodeById(nodeInfo.document, nodeId);
                        if (targetNode) {
                            console.log(`✅ 在 ${nodeKey} 的子节点中找到目标节点`);
                            break;
                        }
                    }
                }
            }
            // 如果还没找到，尝试在 document.children 中查找
            if (!targetNode && fileData.document && fileData.document.children) {
                console.log(`🔄 在 document.children 中查找...`);
                for (const page of fileData.document.children) {
                    targetNode = findNodeById(page, nodeId);
                    if (targetNode) {
                        console.log(`✅ 在 document.children 中找到目标节点`);
                        break;
                    }
                }
            }
        }
        if (!targetNode) {
            console.error(`❌ 未找到ID为 ${nodeId} 的节点`);
            throw new Error(`未找到ID为 ${nodeId} 的节点`);
        }
        console.log(`✅ 找到节点:`, {
            id: targetNode.id,
            name: targetNode.name,
            type: targetNode.type
        });
        // 返回清理后的节点信息（与渲染进程保持一致）
        const cleanedNode = cleanNodeData(targetNode);
        return {
            success: true,
            data: {
                fileKey: parseResult.data.fileKey,
                nodeId,
                figmaUrl,
                figmaInfo: cleanedNode
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
